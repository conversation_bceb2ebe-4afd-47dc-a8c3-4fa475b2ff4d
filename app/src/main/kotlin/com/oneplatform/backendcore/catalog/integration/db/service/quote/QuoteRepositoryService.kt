package com.oneplatform.backendcore.catalog.integration.db.service.quote

import com.oneplatform.backendcore.catalog.domain.model.quote.Quote
import com.oneplatform.backendcore.catalog.domain.repository.quote.QuoteRepository
import com.oneplatform.backendcore.catalog.domain.request.quote.QuoteUpsertDomainRequest
import com.oneplatform.backendcore.catalog.integration.db.mapper.quote.toJpa
import com.oneplatform.backendcore.catalog.integration.db.repository.quote.QuoteJpaRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service

@Service
class QuoteRepositoryService(
    private val quoteJpaRepository: QuoteJpaRepository,
): QuoteRepository {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    override fun create(
        request: QuoteUpsertDomainRequest
    ): Quote {
        val jpaQuote = request.toJpa()
        logger.info { "Creating quote for request = $request" }
        val savedJpaQuote = quoteJpaRepository.save(jpaQuote)
        return savedJpaQuote.toDomain()
    }

    override fun update(
        request: QuoteUpsertDomainRequest
    ): Quote {
        TODO("Not yet implemented")
    }
}