package com.oneplatform.backendcore.catalog.domain.model.quote

import com.oneplatform.backendcore.catalog.domain.model.Dimension
import com.oneplatform.backendcore.catalog.domain.model.pricing.Pricing
import com.oneplatform.backendcore.core.domain.model.customtag.CustomTag
import com.oneplatform.backendcore.core.domain.model.document.Document

data class QuoteProduct(
    val id: String,
    val productCode: String,
    val quote: String,
    val name: String,
    val description: String? = null,
    val version: String,
    val referenceProduct: QuoteProductReferenceProduct? = null,
    val documents: List<Document>? = null,
    val customTags: List<CustomTag>? = null,
    val dimensions: List<Dimension>? = null,
    val pricing: Pricing
)
