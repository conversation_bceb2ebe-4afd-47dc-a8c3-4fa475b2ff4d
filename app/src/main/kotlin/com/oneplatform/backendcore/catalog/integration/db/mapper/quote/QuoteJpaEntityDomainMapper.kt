package com.oneplatform.backendcore.catalog.integration.db.mapper.quote

import com.oneplatform.backendcore.catalog.domain.model.quote.Quote
import com.oneplatform.backendcore.catalog.integration.db.entity.quote.JpaQuote

fun JpaQuote.toDomain(): Quote = Quote(
    id = this.id,
    description = this.description,
    companyId = this.companyId,
    customerId = this.customerId,
    products = emptyList(),
    status = this.status,
    version = this.customVersion.toString(),
    documents = null,
    customTags = null,
    quoteListPrice = this.listPrice,
    quoteSellingPrice = this.sellingPrice,
    discount = this.discounts.
)