package com.oneplatform.backendcore.catalog.domain.request.quote

import com.oneplatform.backendcore.catalog.domain.model.pricing.Discount
import com.oneplatform.backendcore.core.domain.model.amount.Amount
import com.oneplatform.backendcore.core.domain.model.customtag.CustomTag
import com.oneplatform.backendcore.core.domain.model.document.Document
import java.time.Instant

data class QuoteUpsertDomainRequest(
    val quoteId: String? = null,
    val description: String? = null,
    val companyId: String,
    val customerId: String,
    val createdDate: Instant,
    val listPrice: Amount,
    val sellingPrice: Amount,
    val discounts: List<Discount>,
    val quoteProductsInput: List<QuoteProductUpsertDomainRequest>,
    val documents: Set<Document>? = emptySet(),
    val customTags: Set<CustomTag>? = emptySet(),
)